{% include 'header.html' %} {% from 'style.html' import breadcrumb_line_svg, input_down_arrow_offer_svg, select_network_svg, select_address_svg, select_swap_type_svg, select_bid_amount_svg, select_rate_svg, step_one_svg, step_two_svg, step_three_svg %}
<div class="container mx-auto">
  <section class="p-5 mt-5">
    <div class="flex flex-wrap items-center -m-2">
      <div class="w-full md:w-1/2 p-2">
        <ul class="flex flex-wrap items-center gap-x-3 mb-2">
          <li><a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
              <p>Home</p>
            </a>
          </li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
          <li><a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/newoffer">Place</a></li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
        </ul>
      </div>
    </div>
  </section>
  <section class="py-4">
    <div class="container px-4 mx-auto">
      <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
        <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
        <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
        <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
        <div class="relative z-20 flex flex-wrap items-center -m-3">
          <div class="w-full md:w-1/2 p-3">
            <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Place an New Offer</h2>
            <p class="font-normal text-coolGray-200 dark:text-white">Place an order on the network order book.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  {% include 'inc_messages.html' %}
  <section>
    <div class="p-5 mb-5">
      <div class="mx-4 p-4">
        <div class="flex items-center">

          <div class="flex items-center text-teal-600 relative">
            <div class="rounded-full h-12 w-12 py-3 border-2 border-blue-500">
            {{ step_one_svg | safe }}
          </div>
            <div class="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium uppercase dark:text-white text-gray-700">Step 1 - Place</div>
          </div>

          <div class="flex-auto border-t-2 border-gray-300 dark:border-gray-400"></div>

          <div class="flex items-center text-teal-600 relative">
            <div class="rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 border-gray-300 dark:border-gray-400">
            {{ step_two_svg | safe }}
          </div>
            <div class="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium uppercase dark:text-white text-gray-700">Step 2 - Setup</div>
          </div>

          <div class="flex-auto border-t-2 border-gray-300 dark:border-gray-400"></div>

          <div class="flex items-center text-gray-500 relative">
            <div class="rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 border-gray-300 dark:border-gray-400">
            {{ step_three_svg | safe }}
          </div>
            <div class="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium uppercase dark:text-white text-gray-700">Confirm</div>
          </div>

        </div>
      </div>
    </div>
  </section>
  <section class="py-3">
    <div class="container px-4 mx-auto">
      <div class="p-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
        <div class="flex flex-wrap items-center justify-between -mx-4 pb-6 border-gray-400 border-opacity-20"> </div>
        <form method="post" autocomplete="off" id="form">
          <div class="py-3 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Select Network</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="relative">
                    {{ input_down_arrow_offer_svg | safe }}
                    <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                    {{ select_network_svg | safe }}
                  </div>
                    <select class="pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="addr_to">
                      <option{% if data.addr_to=="-1" %} selected{% endif %} value="-1">Public Network</option>
                        {% for a in addrs_to %}
                        <option{% if data.addr_to==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[0] }} ({{ a[1] }})</option>
                        {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
<div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
  <div class="w-full md:w-10/12">
    <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
      <div class="w-full md:w-1/3 p-6">
        <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Select Address</p>
      </div>
      <div class="w-full md:flex-1 p-3">
        <div class="relative">
          {{ input_down_arrow_offer_svg | safe }}
          <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
          {{ select_address_svg | safe }}
          </div>
          <select class="hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="addr_from">
            <option{% if data.addr_from=="-1" %} selected{% endif %} value="-1">New Address</option>
            {% for a in addrs %}
            <option{% if data.addr_from==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[0] }} {{ a[1] }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
    </div>
  </div>
</div>

          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Swap Type</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="relative">
                    {{ input_down_arrow_offer_svg | safe }}
                    <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                    {{ select_swap_type_svg | safe }}
                  </div>
                  <select class="hover:border-blue-500 bg-gray-50 pl-10 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" name="swap_type" id="swap_type">
                    {% for a in swap_types %}
                    <option{% if data.swap_type==a[0] %} selected{% endif %} value="{{ a[0] }}">{{ a[1] }}</option>
                    {% endfor %}
                  </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">You Send</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                       <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Select Coin You Send</p>
                      <div class="custom-select">
                        <div class="relative">
                          {{ input_down_arrow_offer_svg | safe }}
                          <select class="select hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 bold focus:ring-0" id="coin_from" name="coin_from" onchange="set_rate('coin_from');">
                            <option value="-1">Select coin you send</option>
                            {% for c in coins_from %}
                            <option{% if data.coin_from==c[0] %} selected{% endif %} value="{{ c[0] }}" data-image="/static/images/coins/{{ c[1]|replace(" ", "-") }}-20.png" data-balance="{{ c[2] }}">{{ c[1] }} - Balance: {{ c[2] }}</option>
                            {% endfor %}
                          </select>
                          <div class="select-dropdown"> <img class="select-icon" src="" alt=""> <img class="select-image" src="" alt=""> </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Amount You Send</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"> </div> <input class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 focus:ring-0 bold" placeholder="0" type="text" id="amt_from" name="amt_from" value="{{ data.amt_from }}" onchange="set_rate('amt_from');">
                      </div>
                      <div class="mt-2 flex space-x-2">
                        <button type="button" class="hidden md:block py-1 px-2 bg-blue-500 text-white text-lg lg:text-sm rounded-md focus:outline-none" onclick="setOfferAmount(0.25, 'amt_from')">25%</button>
                        <button type="button" class="hidden md:block py-1 px-2 bg-blue-500 text-white text-lg lg:text-sm rounded-md focus:outline-none" onclick="setOfferAmount(0.5, 'amt_from')">50%</button>
                        <button type="button" class="py-1 px-2 bg-blue-500 text-white text-lg lg:text-sm rounded-md focus:outline-none" onclick="setOfferAmount(1, 'amt_from')">100%</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">You Get</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Select Coin You Get</p>
                      <div class="custom-select">
                        <div class="relative">
                          {{ input_down_arrow_offer_svg | safe }}
                          <select class="select hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 bold focus:ring-0" id="coin_to" name="coin_to" onchange="set_rate('coin_to');">
                            <option value="-1">Select coin you get</option>
                            {% for c in coins %}
                            <option{% if data.coin_to==c[0] %} selected{% endif %} value="{{ c[0] }}" data-image="/static/images/coins/{{ c[1]|replace(" ", "-") }}-20.png">{{ c[1] }}</option>
                            {% endfor %}
                          </select>
                          <div class="select-dropdown"> <img class="select-icon" src="" alt=""> <img class="select-image" src="" alt=""> </div>
                        </div>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base text-coolGray-800 dark:text-white">Amount You Get</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"> </div> <input class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-5 focus:ring-0 bold" placeholder="0" type="text" id="amt_to" name="amt_to" value="{{ data.amt_to }}" onchange="set_rate('amt_to');">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Bid Amount</p>
                </div>
                <div class="w-full md:flex-1 p-3">
                  <div class="flex flex-wrap -m-3">
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base dark:text-white text-coolGray-800">Minimum Purchase</p>
                      <div class="relative">
                        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        {{ select_bid_amount_svg | safe }}
                      </div>
                      <input class="pl-10 hover:border-blue-500 pl-10 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="text" id="amt_bid_min" name="amt_bid_min" value="{{ data.amt_bid_min }}" title="Bids with an amount below the minimum bid value will be discarded">
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-3">
                      <p class="mb-1.5 font-medium text-base dark:text-white text-coolGray-800">Rate</p>             
                      <div class="flex items-center gap-2">
                        <div class="relative flex-1">
                          <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                            {{ select_rate_svg | safe }}
                          </div>
                          <input class="pl-10 hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-white text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0" type="text" id="rate" name="rate" value="{{ data.rate }}" onchange="set_rate('rate');">
                        </div>
                        <button type="button" id="get_rate_inferred_button" class="px-4 py-2.5 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-md shadow-sm focus:outline-none">Get Rate Inferred</button>
                      </div>
                      <div class="flex form-check form-check-inline mt-5">
                        <div class="flex items-center h-5"> <input class="form-check-input hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="rate_lock" name="rate_lock" value="rl" checked="checked"> </div>
                        <div class="ml-2 text-sm">
                          <label class="form-check-label text-sm font-medium text-gray-800 dark:text-white" for="inlineCheckbox1">Lock Rate</label>
                          <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300">Automatically adjusts the <b>You Get</b> value based on the rate you’ve entered. Without it, the rate value is automatically adjusted based on the number of coins you put in <b>You Get.</b></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
<div class="py-0 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
  <div class="w-full md:w-10/12">
    <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
      <div class="w-full md:w-1/3 p-6">
        <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Options</p>
      </div>
      <div class="w-full md:flex-1 p-3">
        <div class="flex form-check form-check-inline">
          <div class="flex items-center h-5"> 
            {% if debug_ui_mode == true %}
            <input class="hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="amt_var" name="amt_var" value="av">
            {% else %}
            <input class="cursor-not-allowed hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="amt_var" name="amt_var" value="av" checked disabled>
            {% endif %}
          </div>
          <div class="ml-2 text-sm">
            <label class="form-check-label text-sm font-medium text-gray-800 dark:text-white" for="inlineCheckbox2">Amount Variable</label>
            <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300">Allow bids with a different amount to the offer.</p>
          </div>
        </div>
        <div class="flex mt-2 form-check form-check-inline">
          <div class="flex items-center h-5"> 
            {% if debug_ui_mode == true %}
            <input class="hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="rate_var" name="rate_var" value="rv"> 
            {% else %}
            <input class="cursor-not-allowed hover:border-blue-500 w-5 h-5 form-check-input text-blue-600 bg-gray-50 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-500 dark:border-gray-400" type="checkbox" id="rate_var" name="rate_var" value="rv" disabled>
            {% endif %}
          </div>
          <div class="ml-2 text-sm">
            <label class="form-check-label text-sm font-medium text-gray-800 dark:text-white" for="inlineCheckbox3">Rate Variable</label>
            <p id="helper-checkbox-text" class="text-xs font-normal text-gray-500 dark:text-gray-300">Allow bids with a different rate to the offer.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
          <div class="pricejsonhidden hidden py-3 border-b items-center justify-between -mx-4 mb-6 pb-3 border-gray-400 border-opacity-20">
            <div class="w-full md:w-10/12">
              <div class="flex flex-wrap -m-3 w-full sm:w-auto px-4 mb-6 sm:mb-0">
                <div class="w-full md:w-1/3 p-6">
                  <p class="text-sm text-coolGray-800 dark:text-white font-semibold">Coin Prices/Rates (JSON)</p>
                </div>
                <div class="w-full md:flex-1 p-3 dark:text-white text-sm monospace">
                  <p id="rates_display"></p>
                </div>
              </div>
            </div>
          </div>
      </div>
    </div>
  </section>
  <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
      <div class="pb-6 border-coolGray-100">
        <div class="flex flex-wrap items-center justify-between -m-2">
          <div class="w-full pt-2">
            <div class="container mt-5 mx-auto">
              <div class="pt-6 pb-6 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
                <div class="px-6">
                  <div class="flex flex-wrap justify-end">
                    <div class="w-full md:w-auto p-1.5">
                      <button name="check_rates" type="button" value="Check Current Prices/Rates (JSON)" onclick='lookup_rates();' class="flex flex-wrap justify-center w-full px-4 py-2.5 font-medium text-sm text-coolGray-500 hover:text-coolGray-600 border border-coolGray-200 hover:border-coolGray-300 bg-white rounded-md focus:ring-0 focus:outline-none dark:text-white dark:hover:text-white dark:bg-gray-600 dark:hover:bg-gray-700 dark:border-gray-600 dark:hover:border-gray-600">
                        <span>Check Current Prices/Rates (JSON)</span>
                    </button>
                  </div>
                    <div class="w-full md:w-auto p-1.5">
                      <button name="continue" value="Continue" type="submit" class="flex flex-wrap justify-center w-full px-4 py-2.5 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none">
                        <span>Continue</span>
                      </button>
                    </div>
                    <input type="hidden" name="formid" value="{{ form_id }}">
                    <input type="hidden" name="step1" value="a">
                  </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<script>
function setOfferAmount(percent, fieldId) {
  const amountInput = document.getElementById(fieldId);
  const coinFromSelect = document.getElementById('coin_from');

  if (!amountInput || !coinFromSelect) {
    console.error('Required elements not found');
    return;
  }

  const selectedOption = coinFromSelect.options[coinFromSelect.selectedIndex];
  if (!selectedOption || selectedOption.value === '-1') {
    alert('Please select a coin first');
    return;
  }

  const balance = selectedOption.getAttribute('data-balance');
  if (!balance) {
    console.error('Balance not found for selected coin');
    return;
  }

  const floatBalance = parseFloat(balance);
  if (isNaN(floatBalance) || floatBalance <= 0) {
    alert('Invalid balance for selected coin');
    return;
  }

  const calculatedAmount = floatBalance * percent;
  amountInput.value = calculatedAmount.toFixed(8);

  // Trigger the onchange event to update rates
  if (amountInput.onchange) {
    amountInput.onchange();
  }

  // Also trigger the set_rate function if it exists
  if (typeof set_rate === 'function') {
    set_rate(fieldId);
  }
}



// Function to update custom dropdown display with three-line format
function updateCustomDropdownDisplay(select, coinName, balance, pending) {
  const selectNameElement = select.nextElementSibling?.querySelector('.select-name');
  if (!selectNameElement) return;

  // Create three-line stacked display
  selectNameElement.innerHTML = '';
  selectNameElement.style.display = 'flex';
  selectNameElement.style.flexDirection = 'column';
  selectNameElement.style.alignItems = 'flex-start';
  selectNameElement.style.lineHeight = '1.2';

  // Coin name (first line)
  const coinNameDiv = document.createElement('div');
  coinNameDiv.textContent = coinName;
  coinNameDiv.style.fontWeight = 'normal';
  coinNameDiv.style.color = 'inherit';

  // Balance (second line)
  const balanceDiv = document.createElement('div');
  balanceDiv.textContent = `Balance: ${balance}`;
  balanceDiv.style.fontSize = '0.75rem';
  balanceDiv.style.color = '#6b7280'; // gray-500
  balanceDiv.style.marginTop = '1px';

  selectNameElement.appendChild(coinNameDiv);
  selectNameElement.appendChild(balanceDiv);

  // Pending (third line, if available)
  if (pending !== '0.0' && parseFloat(pending) > 0) {
    const pendingDiv = document.createElement('div');
    pendingDiv.textContent = `+${pending} pending`;
    pendingDiv.style.fontSize = '0.75rem';
    pendingDiv.style.color = '#10b981'; // green-500
    pendingDiv.style.marginTop = '1px';
    selectNameElement.appendChild(pendingDiv);
  }
}

function updateCoinDropdownBalances(balanceData) {
  // Update the coin_from dropdown (only this one shows balances)
  const coinFromSelect = document.getElementById('coin_from');
  if (!coinFromSelect) return;

  // Create a map of coin ID to balance and pending for quick lookup
  const balanceMap = {};
  const pendingMap = {};
  balanceData.forEach(coin => {
    balanceMap[coin.id] = coin.balance;
    pendingMap[coin.id] = coin.pending || '0.0';
  });

  // Update each option in the dropdown
  Array.from(coinFromSelect.options).forEach(option => {
    if (option.value === '-1') return; // Skip the placeholder option

    const coinId = parseInt(option.value);
    const balance = balanceMap[coinId] || '0.0';
    const pending = pendingMap[coinId] || '0.0';

    // Update the data-balance attribute
    option.setAttribute('data-balance', balance);

    // Update the display text with balance and pending (if any) - using three-line format like AMM
    const coinName = option.textContent.split(' - Balance:')[0]; // Get coin name before balance
    if (pending !== '0.0' && parseFloat(pending) > 0) {
      option.textContent = `${coinName} - Balance: ${balance} (+${pending} pending)`;
    } else {
      option.textContent = `${coinName} - Balance: ${balance}`;
    }

    // Also update the custom dropdown display if this is the selected option
    const select = option.closest('select');
    if (select && select.value === option.value) {
      updateCustomDropdownDisplay(select, coinName, balance, pending);
    }
  });
}

// Function to fetch balance data
function fetchBalanceData() {
  return fetch('/json/walletbalances', {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      }
      return response.json();
    })
    .then(balanceData => {
      if (balanceData.error) {
        throw new Error(balanceData.error);
      }

      // Expect array format
      if (!Array.isArray(balanceData)) {
        throw new Error('Invalid response format');
      }

      return balanceData;
    });
}

// Function to automatically refresh balances via WebSocket
function setupWebSocketBalanceUpdates() {
  if (window.WebSocketManager && typeof window.WebSocketManager.addMessageHandler === 'function') {


    // Register with WebSocket Manager for balance update events
    const balanceHandlerId = window.WebSocketManager.addMessageHandler('message', (data) => {
      if (data && data.event) {
        // Listen for direct balance update events from blockchain monitoring
        if (data.event === 'balance_updated') {


          // Debounce multiple rapid events (new blocks can come quickly)
          clearTimeout(window.offerBalanceUpdateTimeout);
          window.offerBalanceUpdateTimeout = setTimeout(() => {
            fetchBalanceData()
              .then(balanceData => {
                updateCoinDropdownBalances(balanceData);

              })
              .catch(error => {
                console.error('Error updating New Offer balances via WebSocket:', error);
              });
          }, 2000); // 2 second debounce for block events
        }

        // Also listen for swap events that might affect balances immediately
        const swapEvents = ['new_bid', 'bid_accepted', 'swap_completed'];
        if (swapEvents.includes(data.event)) {


          // Longer delay for swap events (balance changes take time to confirm)
          clearTimeout(window.offerSwapEventTimeout);
          window.offerSwapEventTimeout = setTimeout(() => {
            fetchBalanceData()
              .then(balanceData => {
                updateCoinDropdownBalances(balanceData);

              })
              .catch(error => {
                console.error('Error updating New Offer balances via swap event:', error);
              });
          }, 5000); // 5 second delay for swap events
        }
      }
    });

    // Store handler ID for cleanup
    window.offerBalanceHandlerId = balanceHandlerId;

  } else {

  }

  // Set up periodic balance refresh (every 2 minutes) as fallback for missed events
  if (!window.offerPeriodicRefresh) {
    window.offerPeriodicRefresh = setInterval(() => {
      fetchBalanceData()
        .then(balanceData => {
          updateCoinDropdownBalances(balanceData);

        })
        .catch(error => {
          console.error('Error in periodic New Offer balance refresh:', error);
        });
    }, 120000); // 2 minutes (reduced since we have real-time updates)


  }
}

// Cleanup function for page unload
function cleanupOfferBalanceUpdates() {
  if (window.offerBalanceHandlerId && window.WebSocketManager) {
    window.WebSocketManager.removeMessageHandler('message', window.offerBalanceHandlerId);

  }

  if (window.offerPeriodicRefresh) {
    clearInterval(window.offerPeriodicRefresh);
    window.offerPeriodicRefresh = null;

  }

  clearTimeout(window.offerBalanceUpdateTimeout);
  clearTimeout(window.offerSwapEventTimeout);
}

// Initialize WebSocket balance updates when page loads
document.addEventListener('DOMContentLoaded', function() {
  setupWebSocketBalanceUpdates();

  // Register cleanup with CleanupManager if available
  if (window.CleanupManager) {
    window.CleanupManager.registerResource('offerBalanceUpdates', null, cleanupOfferBalanceUpdates);
  }

  // Fallback cleanup on page unload
  window.addEventListener('beforeunload', cleanupOfferBalanceUpdates);


});

</script>

<script src="static/js/new_offer.js"></script>
{% include 'footer.html' %}
</div>
</body>
</html>
